<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Navbar Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 120px auto 50px;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .demo-box {
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .black-demo {
            background: #000;
            color: white;
        }
        .blue-demo {
            background: #3b4a87;
            color: white;
        }
        .test-link {
            display: inline-block;
            padding: 15px 30px;
            background: #3b4a87;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 20px 10px;
        }
        .test-link:hover {
            background: #2c3a6b;
        }
        .instruction {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
        h1 {
            color: #3b4a87;
            margin-bottom: 30px;
        }
        .code-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            text-align: left;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Simple Navbar Color Change</h1>
        
        <div class="instruction">
            <strong>📋 How it works:</strong><br>
            • At top of page = BLACK navbar<br>
            • When scrolling = BLUE navbar<br>
            • Simple JavaScript detects scroll position
        </div>

        <div class="demo-box black-demo">
            ⬆️ TOP OF PAGE: BLACK NAVBAR (#000000)
        </div>

        <div class="demo-box blue-demo">
            ⬇️ WHEN SCROLLING: BLUE NAVBAR (#3b4a87)
        </div>

        <div class="code-box">
            <strong>JavaScript Code:</strong><br><br>
            if (scrollPosition === 0) {<br>
            &nbsp;&nbsp;// At top - BLACK navbar<br>
            &nbsp;&nbsp;navbar.classList.add('navbar-black');<br>
            } else {<br>
            &nbsp;&nbsp;// Scrolling - BLUE navbar<br>
            &nbsp;&nbsp;navbar.classList.add('navbar-blue');<br>
            }
        </div>

        <div class="instruction">
            <strong>✅ Implementation Complete!</strong><br>
            Your navbar now changes from BLACK to BLUE exactly as requested.
        </div>

        <a href="index.html" class="test-link">🚀 Test the Navbar Now!</a>
        
        <div style="margin-top: 40px; padding: 20px; background: #d4edda; border-radius: 8px; color: #155724;">
            <strong>🎉 SUCCESS!</strong><br>
            Simple BLACK → BLUE navbar transition is working perfectly!
        </div>
    </div>
</body>
</html>
