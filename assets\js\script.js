$(document).ready(function () {
  $("#slider-1").owlCarousel({
    autoplay: true,
    autoplayTimeout: 1000,
    autoplayHoverPause: false
  });
}); // ✅ Properly closed


// FAQ highlight script
document.addEventListener('DOMContentLoaded', function() {
  const faqItems = document.querySelectorAll(".faq-item");

  faqItems.forEach(item => {
    item.addEventListener("click", () => {
      faqItems.forEach(el => el.classList.remove("active"));
      item.classList.add("active");
    });
  });
});

// Place this at the end of body or after DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
  const header = document.querySelector('header.head');
  const menuToggle = header.querySelector('.menu-toggle');
  const closeBtn = header.querySelector('.close-btn');
  const mobileNav = header.querySelector('.mobile-nav');

  // Open mobile menu
  if (menuToggle) {
    menuToggle.addEventListener('click', function() {
      if (mobileNav) {
        mobileNav.style.display = mobileNav.style.display === 'block' ? 'none' : 'block';
      }
    });
  }

  // Close on close button click
  if (closeBtn) {
    closeBtn.addEventListener('click', function() {
      if (mobileNav) {
        mobileNav.style.display = 'none';
      }
    });
  }
});

