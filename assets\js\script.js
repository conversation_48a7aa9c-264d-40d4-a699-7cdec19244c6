$(document).ready(function () {
  $("#slider-1").owlCarousel({
    autoplay: true,
    autoplayTimeout: 1000,
    autoplayHoverPause: false
  });
}); // ✅ Properly closed


// FAQ highlight script
// const faqItems = document.querySelectorAll(".faq-item");

// faqItems.forEach(item => {
//   item.addEventListener("click", () => {
//     faqItems.forEach(el => el.classList.remove("active"));
//     item.classList.add("active");
//   });
// });

// Place this at the end of body or after DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
  const header = document.querySelector('header.head');
  const menuToggle = header.querySelector('.menu-toggle');
  const closeBtn = header.querySelector('.mobile-close-btn');
  const overlay = header.querySelector('.mobile-menu-overlay');

  // Open mobile menu
  menuToggle.addEventListener('click', function() {
    header.classList.add('menu-open');
  });
  // Close on close button or overlay click
  [closeBtn, overlay].forEach((el) => {
    el.addEventListener('click', function() {
      header.classList.remove('menu-open');
    });
  });
});

