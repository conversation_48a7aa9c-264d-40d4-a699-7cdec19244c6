$(document).ready(function () {
  // Owl Carousel
  $("#slider-1").owlCarousel({
    autoplay: true,
    autoplayTimeout: 1000,
    autoplayHoverPause: false
  });

  // Simple Navbar Color Change - BLACK at top, BLUE when scrolling
  function updateNavbar() {
    var scrollTop = $(window).scrollTop();

    if (scrollTop <= 10) {
      // At top - BLACK navbar
      $('header.head').removeClass('navbar-blue').addClass('navbar-black');
    } else {
      // Scrolling - BLUE navbar
      $('header.head').removeClass('navbar-black').addClass('navbar-blue');
    }
  }

  // Check on page load
  updateNavbar();

  // Check when scrolling
  $(window).scroll(function() {
    updateNavbar();
  });

  // FAQ highlight script
  $(".faq-item").click(function() {
    $(".faq-item").removeClass("active");
    $(this).addClass("active");
  });

  // Mobile Menu Functions
  function openMobileMenu() {
    $('header.head').addClass('menu-open');
    $('body').css('overflow', 'hidden');
  }

  function closeMobileMenu() {
    $('header.head').removeClass('menu-open');
    $('body').css('overflow', '');
  }

  // Open mobile menu on hamburger click
  $('.menu-toggle').click(function(e) {
    e.preventDefault();
    openMobileMenu();
  });

  // Close mobile menu on close button click
  $('.mobile-close-btn').click(function(e) {
    e.preventDefault();
    closeMobileMenu();
  });

  // Close mobile menu on overlay click
  $('.mobile-menu-overlay').click(function() {
    closeMobileMenu();
  });

  // Close mobile menu on escape key
  $(document).keydown(function(e) {
    if (e.key === 'Escape' && $('header.head').hasClass('menu-open')) {
      closeMobileMenu();
    }
  });

  // Close mobile menu when clicking on nav links
  $('.mobile-nav-link:not(.mobile-dropdown-toggle)').click(function() {
    closeMobileMenu();
  });

  // Handle mobile dropdown toggle with plus icon animation
  $('.mobile-dropdown-toggle').click(function(e) {
    e.preventDefault();
    var $dropdownItem = $(this).closest('.mobile-dropdown');
    var $dropdownMenu = $dropdownItem.find('.mobile-dropdown-menu');

    // Toggle active class for animation
    $dropdownItem.toggleClass('active');

    // Toggle dropdown visibility
    if ($dropdownItem.hasClass('active')) {
      $dropdownMenu.show();
    } else {
      $dropdownMenu.hide();
    }
  });

  // Close mobile menu when clicking on dropdown items
  $('.mobile-dropdown-item').click(function() {
    closeMobileMenu();
  });

});

