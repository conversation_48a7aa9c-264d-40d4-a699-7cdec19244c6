$(document).ready(function () {
  $("#slider-1").owlCarousel({
    autoplay: true,
    autoplayTimeout: 1000,
    autoplayHoverPause: false
  });
}); // ✅ Properly closed


// FAQ highlight script
document.addEventListener('DOMContentLoaded', function() {
  const faqItems = document.querySelectorAll(".faq-item");

  faqItems.forEach(item => {
    item.addEventListener("click", () => {
      faqItems.forEach(el => el.classList.remove("active"));
      item.classList.add("active");
    });
  });
});

// Mobile Menu Functionality
document.addEventListener('DOMContentLoaded', function() {
  const header = document.querySelector('header.head');
  const menuToggle = header.querySelector('.menu-toggle');
  const mobileCloseBtn = header.querySelector('.mobile-close-btn');
  const mobileMenuOverlay = header.querySelector('.mobile-menu-overlay');
  const body = document.body;

  // Function to open mobile menu
  function openMobileMenu() {
    header.classList.add('menu-open');
    body.style.overflow = 'hidden';
  }

  // Function to close mobile menu
  function closeMobileMenu() {
    header.classList.remove('menu-open');
    body.style.overflow = '';
  }

  // Open mobile menu on hamburger click
  if (menuToggle) {
    menuToggle.addEventListener('click', function(e) {
      e.preventDefault();
      openMobileMenu();
    });
  }

  // Close mobile menu on close button click
  if (mobileCloseBtn) {
    mobileCloseBtn.addEventListener('click', function(e) {
      e.preventDefault();
      closeMobileMenu();
    });
  }

  // Close mobile menu on overlay click
  if (mobileMenuOverlay) {
    mobileMenuOverlay.addEventListener('click', function() {
      closeMobileMenu();
    });
  }

  // Close mobile menu on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && header.classList.contains('menu-open')) {
      closeMobileMenu();
    }
  });

  // Close mobile menu when clicking on nav links (for single page navigation)
  const mobileNavLinks = header.querySelectorAll('.mobile-nav-link:not(.mobile-dropdown-toggle)');
  mobileNavLinks.forEach(link => {
    link.addEventListener('click', function() {
      closeMobileMenu();
    });
  });

  // Handle mobile dropdown toggle with plus icon animation
  const mobileDropdownToggle = header.querySelector('.mobile-dropdown-toggle');
  if (mobileDropdownToggle) {
    mobileDropdownToggle.addEventListener('click', function(e) {
      e.preventDefault();
      const dropdownItem = this.closest('.mobile-dropdown');
      const dropdownMenu = dropdownItem.querySelector('.mobile-dropdown-menu');

      if (dropdownItem && dropdownMenu) {
        // Toggle active class for animation
        dropdownItem.classList.toggle('active');

        // Toggle dropdown visibility
        if (dropdownItem.classList.contains('active')) {
          dropdownMenu.style.display = 'block';
        } else {
          dropdownMenu.style.display = 'none';
        }
      }
    });
  }

  // Close mobile menu when clicking on dropdown items
  const mobileDropdownItems = header.querySelectorAll('.mobile-dropdown-item');
  mobileDropdownItems.forEach(item => {
    item.addEventListener('click', function() {
      closeMobileMenu();
    });
  });
});

