html, body {overflow-x: hidden;width: 100%;}

/* Header Styles */
.logo-img{
    width: 60px;
    padding-top: 25px;
}

.head {
    position: relative;
    z-index: 4;
    transition: all 0.3s ease;
}

/* BLACK navbar - at top of page */
.head.navbar-black,
header.head.navbar-black {
    background: #000000 !important;
    background-color: #000000 !important;
    box-shadow: none !important;
}

/* BLUE navbar - when scrolling */
.head.navbar-blue,
header.head.navbar-blue {
    background: #3b4a87 !important;
    background-color: #3b4a87 !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1) !important;
}
.phone-no {
    color: white;
    font-size: 17px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn.nav-demo {
    font-size: 17px;
    min-width: 120px;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-item a {
    font-weight: 600;
    font-size: 17px;
    transition: all 0.3s ease;
}

/* Text color for both BLACK and BLUE navbar */
.head .nav-item a,
.head .phone-no {
    color: white !important;
}

/* Button styling for both states */
.head .btn.nav-demo {
    background: white;
    color: #333;
    border: 2px solid white;
}

.head .btn.nav-demo:hover {
    background: transparent;
    color: white;
    border: 2px solid white;
}

/* Desktop dropdown styles */
.nav-item.dropdown .dropdown-menu {
    background: #fff;
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    margin-top: 10px;
    min-width: 250px;
}

.nav-item.dropdown .dropdown-item {
    padding: 12px 20px;
    font-size: 15px;
    color: #333;
    transition: all 0.3s ease;
}

.nav-item.dropdown .dropdown-item:hover {
    background: #f38b32;
    color: #fff;
}

/* Hamburger Styles */
.menu-toggle {
  display: flex;
  flex-direction: column;
  background: transparent;
  border: none;
  width: 36px;
  height: 34px;
  padding: 4px;
  align-items: flex-start;
  justify-content: center;
  cursor: pointer;
  z-index: 105;
}
.menu-toggle .menu-bar {
  display: block;
  width: 24px;
  height: 3px;
  background: #f38b32;
  margin: 3px 0;
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* Hamburger open animation */
.menu-open .menu-toggle .menu-bar:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}
.menu-open .menu-toggle .menu-bar:nth-child(2) {
  opacity: 0;
}
.menu-open .menu-toggle .menu-bar:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Mobile Nav */
.mobile-nav {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 350px;
  background: #f8f9fa;
  z-index: 110;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.mobile-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30px 25px 20px;
  background: #f8f9fa;
}

.mobile-logo-container {
  display: flex;
  align-items: center;
}

.mobile-logo-img {
  width: 80px;
  height: auto;
}

.mobile-close-btn {
  background: #ffe5e5;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-close-btn i {
  color: #ff6b6b;
  font-size: 16px;
}

.mobile-close-btn:hover {
  background: #ffcccc;
}

.mobile-menu-content {
  padding: 20px 0;
}

.mobile-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav-item {
  margin-bottom: 5px;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 25px;
  color: #6b7280;
  text-decoration: none;
  font-size: 18px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-bottom: 1px solid #e5e7eb;
}

.mobile-nav-link:hover {
  color: #374151;
  background: #ffffff;
  text-decoration: none;
}

.mobile-dropdown-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e5e7eb;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.mobile-dropdown-icon i {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.3s ease;
}

.mobile-dropdown.active .mobile-dropdown-icon {
  background: #3b82f6;
}

.mobile-dropdown.active .mobile-dropdown-icon i {
  color: white;
  transform: rotate(45deg);
}

.mobile-dropdown-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  background: #ffffff;
  display: none;
  border-top: 1px solid #e5e7eb;
}

.mobile-dropdown.active .mobile-dropdown-menu {
  display: block;
}

.mobile-dropdown-item {
  display: block;
  padding: 15px 50px;
  color: #6b7280;
  text-decoration: none;
  font-size: 16px;
  font-weight: 400;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f3f4f6;
}

.mobile-dropdown-item:hover {
  color: #3b82f6;
  background: #f8faff;
  text-decoration: none;
}

.mobile-action-buttons {
  padding: 30px 25px 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-btn {
  padding: 15px 25px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  width: 100%;
}

.mobile-btn-outline {
  background: transparent;
  color: #3b82f6;
  border: 2px solid #3b82f6;
}

.mobile-btn-outline:hover {
  background: #3b82f6;
  color: white;
}

.mobile-btn-primary {
  background: #3b82f6;
  color: white;
}

.mobile-btn-primary:hover {
  background: #2563eb;
}

/* Menu Open State */
.menu-open .mobile-nav {
  transform: translateX(0);
}

.menu-open .mobile-menu-overlay {
  display: block;
  opacity: 1;
}

.menu-open body {
  overflow: hidden;
}
.close-btn{
    background: rgba(255, 107, 53, .12);
    width: 30px;
    height: 30px;
    text-align: center;
    outline: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    border: none;
    border-radius: 20px;
}




/* Hero Section */
.smart {
    position: relative;
    background-color: black;
    padding: 140px 0;
    align-items: center;
}
.smart-header{
    margin-left: -35px;
}
.smart::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 105%;
    background: url("../images/ca-hero-bg.png") no-repeat center;
    z-index: 0;
}
.smart::after {
    content: '';
    position: absolute;
    top: -135px;
    left: 455px;
    width: 109%;
    height: 95%;
    background: url("../images/heading-shape.png") no-repeat center;
    z-index: 1;
}

.smart h1,
.smart-text {
    position: relative;
    z-index: 1;
    color: white;
}

.smart h1 {
    font-style: normal;
    font-size: 82px;
    padding-top: 40px;
    padding-bottom: 40px;
    line-height: 1.1;
}

.smart-text {
    padding-top: 80px;
    padding-left: 25px;
    font-size: 17px;
    line-height: 1.6;
}

.smart-img {
    padding: 19px;
    border: 1px solid #ffb86c;
    border-radius: 16px;
    margin-bottom: -175px;
}

.smart-img img {
    border-radius: 8px;
}

.highlight {
    color: #f38b32;
}
/* sofyware-section-start */
.software-heading {
    font-size: 48px;
    font-weight: 700;
    color: #333;
    padding: 60px 0px;
    text-align: center;
    margin-top: 120px;
}

.flex-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 40px;
}

.software-text {
    display: flex;
    align-items: center;
    font-size: 22px;
    font-weight: 700;
    color: #666;
    text-align: left;
}
.software-text i {
    background-color: #ff6b35;
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 18px;
}
.health-section{

    border: 1px solid #ffb86c;
    margin-bottom: -175px;
    border-radius: 16px;
}
.health-heading{padding-left: 36px;}
.health-heading h1{font-size: 35px;font-weight: 700;font-style: normal;text-align: left;}
.health-heading p{font-size: 18px;text-align: start;color: #666;}
.health-text{font-size: 18px;color: #666;text-align: start;}
.health-overview{padding:50px 75px;background-color: white;border-radius: 16px;margin: 18px;}
.health-img{border-radius: 16px;}
/* software-section-end */

/* feature-section-start */
.feature{background-color: black;margin-bottom: -496px;}
.feature-heading{font-size: 48px;font-weight: 700;color:white;margin-bottom: 50px;margin-top: 185px; }
.feature-para{max-width: 70ch;color: #d7d7d7;font-size: 18px;font-weight: 500;}
.feature-list{font-size: 17px;list-style: none;font-weight: 500;}
.shape-img{margin-top: 185px;}
.feature-adv h3{color: white;}
.feature-img{padding: 19px;border: 1px solid #ffb86c;border-radius: 16px;   }
/* feature-section-end */

/* Technologies-section-start */
.Technologies{
    background-color: white;
    padding-bottom: 120px;
}
.tech-h5{position: relative;padding-top: 185px;font-size: 35px;font-style: normal;font-weight: 700;line-height: 70px;}
.tech-h5::after {content: "";position: absolute;top: 87%;left: 57%;width: 96px;height:4px;background: linear-gradient(287.85deg, #f38b32 0%, #FF7E5D 95.32%);}
.tech-h1{font-size: 48px;font-style: normal;font-weight: 700;}
.tech-img{padding-top: 40px;}
/* Technologies-section-end */

/* Ready Software-start */
.Ready-Software{background-image: url(../images/ca-service-section-bg.png);background-size: cover;background-position: center;background-repeat: no-repeat;padding-bottom: 120px;
}
.ready-h1{padding-top: 120px;width: 97%;font-size: 48px;font-weight: 700;font-style: normal;}
.text-stroke {-webkit-text-fill-color: rgba(0, 0, 0, 0);-webkit-text-stroke: 1px #f48a37;font-size: 65px;padding-top: 25px;}
.heading-6{font-size: 20px;font-weight: 700;color: black;}
.digital-service-card{background-color: white;border-radius: 16px;border: 1px solid rgba(239, 62, 62, .15);height: 84%;margin-top: 75px;}
.clr-text{font-size: 17px;color: black;font-weight: 400;padding-top: 6px;padding-left: 26px;padding-right: 36px;}
  /* Ready Software-end  */

  /* testimonial-section-start */
.about{padding: 120px 0px;}
.heading-3{font-size: 48px;font-weight: 700;line-height: 60px;}
.about-para{color: #666;font-size:20px;}
.testimonial{margin-left: 115px;padding-top: 60px;}
.quote-img{width: 55px;margin-left: 1px;}
.testimonial-h6{width: 581px;margin-left: -190px;margin-top: 65px;font-size: 21px;color: black;}
.about-passage{position: relative;margin-top: 219px;margin-left: -293px;}
.about-passage::after{position: absolute;content: " ";top: 16%;left: -18%;width: 2.5rem;height:4px;background: linear-gradient(287.85deg, #f38b32 0%, #FF7E5D 95.32%);}
.about-passage h6{font-size: 22px;margin-left: -40px;}
.about-passage p{font-size: 18px;color: #666;margin-left: -40px;}
.testimonial-1-h6{width: 581px;margin-left: 30px;margin-top: 65px;font-size: 21px;color: black;}
/* testimonial-section-start */

 /* Frequently-setion-start */
.Frequently{background-color: black;padding: 100px; }
.frequently-h1 {color: white;font-size: 48px;font-weight: 700;font-style: normal; }
.faq-header{background-color: #393b3f;color: white;font-weight: 600;border-radius: 6px;padding: 6px 30px; }
.question{margin-top: 80px; }
.faq-item {padding: 25px 40px;background: #1c1c1c;margin-bottom: 20px;border-radius: 5px;cursor: pointer;color: white;font-size: 20px;font-weight: 600;border: 1px solid #2E2E2E;}
.faq-item:hover {background: #fff;color: #000;}
.faq-item.active {background: #fff;color: #000;font-size: 20px;font-weight: 600;}
.faq-list-group{margin-top: 30px;}
.faq-header-1 {background-color: #f55f4e;color: white;font-weight: 600;border-radius: 6px;padding: 6px 30px;}
.tab-para{color: white;font-size: 19px;line-height: 32px;padding: 40px;}
.tab-content{margin-top: 30px;border: 1px solid #ffb86c;border-radius: 16px;}
/* Frequently-setion-end */

/* connect-section-start */
.connect{padding-top: 60px;background: url(../images/contact-us) no-repeat center bottom;padding-bottom: 120px;}
.connect-heading{font-size: 40px;font-weight: 700;font-style: normal;}
.connect-para{font-size: 18px;color:#666;line-height: 40px;}
.register-form{margin-top:40px;}
.contact-img{margin-top: 40px;max-width: 100%;}
/* connect-section-end */

/* footer-section-start */
.footer{background: url(../images/page-header-bg.svg) no-repeat bottom right;background-color: #111827 !important;}
.logo-white{width: 61px;padding-top: 25px;}
.footer-info{padding-top: 40px;}
.footer-overall{padding: 120px 0px;}
.footer-para{ color: white;font-size: 18px;padding-top: 20px;}
.footer-heading{color: white;font-size: 20px;font-weight: 700;}
.footer-nav-list{list-style: none;padding-top: 30px;  }
.text-decoration-none{color: white;font-size: 16px;line-height: 35px;}
.contact-info {list-style: none;padding-top: 30px;line-height: 35px;}
.contact-info li { color: white; margin-bottom: 10px; align-items: center;}
.contact-info i {text-decoration:none;margin-right: 10px;color: #fff;}
.contact-info a {color: white;text-decoration: none; }
.social-icons {display: flex;gap: 10px;margin-left: 30px;}
.social-icons a {display: flex;justify-content: center;align-items: center;width: 35px;height: 35px;border: 1px solid #555;border-radius: 5px;color: #fff;text-decoration: none;}
.social-icons a:hover { background: #fff; color: #000;}
/* footer-section-end */

/* footer-bottom-section-start */
.footer-bottom{background-color: #111827;border-top: 1px solid rgba(248, 249, 250, .05);}
.copyright-text p{margin:20px;color: white;}
/* footer-bottom-section-end */

    /* MOBILE (max-width: 767px) */
@media (max-width: 767px) {
   .logo-img {
    width: 45px;
    padding-top: 10px;
   }

   .menu-toggle {
    margin-right: 0px;
   }

   /* Hero section mobile styles */
   .smart-heading {
    font-size: 32px !important;
    line-height: 38px !important;
    padding: 20px 15px !important;
    text-align: center;
   }

   .smart {
    padding-top: 120px;
    padding-bottom: 40px;
    text-align: center;
   }

   .smart-img {
    padding: 10px;
    border: 1px solid #ffb86c;
    border-radius: 16px;
    margin-bottom: 20px;
   }

   .smart-text {
    padding-top: 20px;
    font-size: 16px;
    line-height: 26px;
    padding-left: 15px;
    padding-right: 15px;
    text-align: center;
   }

   /* Software section mobile */
   .software-heading {
    font-size: 28px !important;
    font-weight: 700;
    color: #333;
    text-align: center;
    margin-top: 20px;
    padding: 0 15px;
   }

   .flex-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 0 15px;
   }

   .software-text {
    text-align: center;
    padding: 15px;
   }

   .health-overview {
    padding: 15px;
    background-color: white;
    border-radius: 16px;
    margin: 15px;
   }

   .health-section {
    border: 1px solid #ffb86c;
    margin-bottom: 20px;
   }

   /* Feature section mobile */
   .feature-heading {
    font-size: 28px !important;
    line-height: 34px !important;
    padding: 0 15px;
    text-align: center;
   }

   .feature-para {
    font-size: 16px;
    line-height: 24px;
    padding: 0 15px;
    text-align: center;
   }

   /* Technologies section mobile */
   .tech-h1 {
    font-size: 28px !important;
    text-align: center;
    padding: 0 15px;
   }

   .tech-h5 {
    text-align: center;
   }

   /* Process section mobile */
   .ready-h1 {
    font-size: 28px !important;
    line-height: 34px !important;
    padding: 0 15px;
   }

   .digital-service-card {
    margin-bottom: 20px;
    text-align: center;
   }

   /* FAQ section mobile */
   .frequently-h1 {
    font-size: 28px !important;
    line-height: 34px !important;
    padding: 0 15px;
   }

   .faq-item {
    font-size: 16px;
    padding: 15px 10px;
   }

   /* Contact section mobile */
   .connect-heading {
    font-size: 28px !important;
    text-align: center;
    margin-bottom: 20px;
   }

   .connect-para {
    text-align: center;
    margin-bottom: 30px;
   }

   /* Footer mobile */
   .footer-heading {
    font-size: 18px;
    margin-bottom: 15px;
   }

   .footer-para {
    font-size: 14px;
    line-height: 22px;
   }
    /* border-radius: 16px;
    margin-left:20px; */
}
.health-heading {
    padding-left: 0px;
}
.health-heading h1 {
    font-size: 24px;
    font-weight: 700;
    font-style: normal;}
.health-text {
    font-size: 18px;
    color: #666;
    text-align: start;
    /* padding-top: 120px; */
}
.health-heading p{
    padding-top: 15px;
}














/* Hide desktop menu on small screens */
@media (max-width: 991px) {
  .d-lg-none { display: block !important; }
  .d-lg-flex, .d-none.d-lg-flex { display: none !important; }

  /* Mobile menu responsive adjustments */
  .mobile-nav {
    width: 280px;
  }
}

/* Extra small screens */
@media (max-width: 576px) {
  .mobile-nav {
    width: 100vw;
    max-width: 100vw;
  }

  .mobile-menu-header {
    padding: 25px 20px 15px;
  }

  .mobile-logo-img {
    width: 70px;
  }

  .mobile-close-btn {
    width: 35px;
    height: 35px;
  }

  .mobile-nav-link {
    padding: 16px 20px;
    font-size: 17px;
  }

  .mobile-dropdown-item {
    padding: 12px 40px;
    font-size: 15px;
  }

  .mobile-action-buttons {
    padding: 25px 20px 15px;
  }

  .mobile-btn {
    padding: 14px 20px;
    font-size: 15px;
  }
}

/* Medium screens */
@media (max-width: 768px) {
  .mobile-nav {
    width: 320px;
  }
}





