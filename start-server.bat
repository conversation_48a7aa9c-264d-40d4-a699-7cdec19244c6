@echo off
echo Starting local web server...
echo.
echo Open your browser and go to: http://localhost:8000
echo Press Ctrl+C to stop the server
echo.

REM Try different methods to start a web server
where python >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using Python HTTP server...
    python -m http.server 8000
    goto :end
)

where node >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Checking for http-server...
    npx http-server --version >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo Using Node.js http-server...
        npx http-server -p 8000
        goto :end
    )
)

echo No suitable web server found.
echo Opening index.html directly in browser...
start index.html

:end
pause
